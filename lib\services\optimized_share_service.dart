import 'dart:io';
import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:mr_garments_mobile/models/message.dart';
import 'package:mr_garments_mobile/utils/snackbar.dart';

/// Optimized share service for fast external sharing of chat messages
class OptimizedShareService {
  /// Share messages with optimized performance
  /// - Closes bottom sheet immediately
  /// - Shows brief loading indicator
  /// - Downloads images in parallel
  /// - Groups content properly with single app promotion
  static Future<void> shareMessagesOptimized({
    required BuildContext context,
    required List<Message> messages,
    required VoidCallback onComplete,
  }) async {
    try {
      // Show loading indicator briefly
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => const Center(
              child: CircularProgressIndicator(color: Color(0xFF005368)),
            ),
      );

      // Process messages in background
      final result = await _prepareShareContent(messages);

      // Close loading dialog
      if (context.mounted) Navigator.pop(context);

      // Open share dialog immediately
      if (result.filesToShare.isNotEmpty) {
        await Share.shareXFiles(result.filesToShare, text: result.shareText);
      } else {
        await Share.share(result.shareText);
      }

      if (context.mounted) {
        AppSnackbar.showSuccess(context, 'Messages shared successfully!');
        onComplete();
      }
    } catch (e) {
      // Close loading dialog if still open
      if (context.mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
      }

      if (context.mounted) {
        AppSnackbar.showError(
          context,
          'Failed to share messages: ${e.toString()}',
        );
      }
    }
  }

  /// Prepare share content with parallel image processing
  static Future<ShareContentResult> _prepareShareContent(
    List<Message> messages,
  ) async {
    String shareContent = '';
    List<XFile> filesToShare = [];
    List<String> imageMessages = [];
    List<String> textMessages = [];
    List<String> catalogMessages = [];

    // Separate messages by type for better organization
    final imageMessagesData =
        messages.where((m) => m.type == MessageType.image).toList();
    final textMessagesData =
        messages.where((m) => m.type == MessageType.text).toList();
    final catalogMessagesData =
        messages.where((m) => m.type == MessageType.catalog).toList();

    // Process text messages
    for (final message in textMessagesData) {
      if (message.text != null && message.text!.isNotEmpty) {
        textMessages.add(message.text!);
      }
    }

    // Process catalog messages
    for (final _ in catalogMessagesData) {
      catalogMessages.add('📚 Catalog shared');
    }

    // Process images in parallel for faster loading
    if (imageMessagesData.isNotEmpty) {
      final futures = imageMessagesData.map(
        (message) => _downloadImage(message),
      );
      final results = await Future.wait(futures);

      for (final result in results) {
        if (result.file != null) {
          filesToShare.add(result.file!);
        }
        if (result.caption != null && result.caption!.isNotEmpty) {
          imageMessages.add(result.caption!);
        }
      }
    }

    // Build organized share content
    if (textMessages.isNotEmpty) {
      shareContent += textMessages.join('\n\n') + '\n\n';
    }

    if (imageMessages.isNotEmpty) {
      shareContent += imageMessages.join('\n\n') + '\n\n';
    }

    if (catalogMessages.isNotEmpty) {
      shareContent += catalogMessages.join('\n\n') + '\n\n';
    }

    // Add single app promotion text at the end
    shareContent +=
        'Shared from Mr. Garments Mobile App\nDownload: https://play.google.com/store/apps/details?id=com.braincave.mrgarments';

    return ShareContentResult(
      shareText: shareContent,
      filesToShare: filesToShare,
    );
  }

  /// Download image with error handling
  static Future<ImageDownloadResult> _downloadImage(Message message) async {
    try {
      if (message.mediaUrl == null) {
        return ImageDownloadResult(caption: '📷 Image');
      }

      final response = await http.get(Uri.parse(message.mediaUrl!));
      if (response.statusCode == 200) {
        final tempDir = await getTemporaryDirectory();
        final fileName = 'shared_image_${message.id}.jpg';
        final file = File('${tempDir.path}/$fileName');
        await file.writeAsBytes(response.bodyBytes);

        return ImageDownloadResult(
          file: XFile(file.path),
          caption: message.text?.isNotEmpty == true ? message.text : null,
        );
      } else {
        return ImageDownloadResult(caption: '📷 Image');
      }
    } catch (e) {
      return ImageDownloadResult(caption: '📷 Image');
    }
  }
}

/// Result class for share content preparation
class ShareContentResult {
  final String shareText;
  final List<XFile> filesToShare;

  ShareContentResult({required this.shareText, required this.filesToShare});
}

/// Result class for image download
class ImageDownloadResult {
  final XFile? file;
  final String? caption;

  ImageDownloadResult({this.file, this.caption});
}
